import 'package:flutter/material.dart';
import 'package:get_it/get_it.dart';
import 'package:go_router/go_router.dart';
import 'package:cached_network_image/cached_network_image.dart';
import 'package:gyawun/generated/l10n.dart';
import 'package:gyawun/ytmusic/ytmusic.dart';
import 'package:gyawun/utils/adaptive_widgets/adaptive_widgets.dart';
import 'package:gyawun/widgets/ios_style_search_bar.dart';
import 'package:gyawun/widgets/ios_style_card.dart';
import 'package:gyawun/widgets/ios_style_transitions.dart';
import 'package:gyawun/services/media_player.dart';
import 'section_item.dart';

class HomeScreen extends StatefulWidget {
  const HomeScreen({super.key});

  @override
  State<HomeScreen> createState() => _HomeScreenState();
}

class _HomeScreenState extends State<HomeScreen> {
  final YTMusic ytMusic = GetIt.I<YTMusic>();
  final ScrollController _scrollController = ScrollController();

  bool initialLoading = true;
  bool nextLoading = false;
  List chips = [];
  List sections = [];
  String? continuation;

  // Professional music recommendations
  List quickPicksSongs = [];
  List recommendedAlbums = [];
  List trendingVideos = [];
  List personalizedPlaylists = [];

  // Additional music suggestions
  List recentlyPlayed = [];
  List moodPlaylists = [];
  List genreRecommendations = [];
  List artistRadio = [];
  List newReleases = [];
  List chartHits = [];
  List relaxingMusic = [];
  List workoutMusic = [];

  @override
  void initState() {
    super.initState();
    fetchHome();
    _scrollController.addListener(_scrollListener);
  }

  @override
  void dispose() {
    _scrollController.removeListener(_scrollListener);
    _scrollController.dispose();
    super.dispose();
  }

  void _scrollListener() {
    if (_scrollController.position.pixels >=
            _scrollController.position.maxScrollExtent - 200 &&
        !nextLoading &&
        continuation != null) {
      fetchNext();
    }
  }

  fetchNext() async {
    if (nextLoading || continuation == null) return;
    setState(() => nextLoading = true);
    Map<String, dynamic> next = await ytMusic.browse(additionalParams: continuation!);
    if (mounted) {
      setState(() {
        nextLoading = false;
        sections.addAll(next['sections'] ?? []);
        continuation = next['continuation'];
      });
    }
  }

  fetchHome() async {
    setState(() {
      initialLoading = true;
      nextLoading = false;
    });

    try {
      // Get main home feed
      Map<String, dynamic> home = await ytMusic.browse();

      // Extract professional music recommendations
      await _extractMusicRecommendations(home['sections'] ?? []);

      if (mounted) {
        setState(() {
          initialLoading = false;
          nextLoading = false;
          chips = home['chips'] ?? [];
          sections = home['sections'] ?? [];
          continuation = home['continuation'];
        });
      }
    } catch (e) {
      print('Error fetching home data: $e');
      if (mounted) {
        setState(() {
          initialLoading = false;
          nextLoading = false;
        });
      }
    }
  }

  Future<void> _extractMusicRecommendations(List sections) async {
    quickPicksSongs.clear();
    recommendedAlbums.clear();
    trendingVideos.clear();
    personalizedPlaylists.clear();
    recentlyPlayed.clear();
    moodPlaylists.clear();
    genreRecommendations.clear();
    artistRadio.clear();
    newReleases.clear();
    chartHits.clear();
    relaxingMusic.clear();
    workoutMusic.clear();

    for (var section in sections) {
      if (section['contents'] == null) continue;

      String sectionTitle = (section['title'] ?? '').toLowerCase();
      List contents = section['contents'];

      // Extract Quick Picks (trending songs, recommended tracks) - Increased limit
      if (sectionTitle.contains('quick') ||
          sectionTitle.contains('mixed') ||
          sectionTitle.contains('recommended') ||
          sectionTitle.contains('for you')) {
        for (var item in contents) {
          if (item['type'] == 'SONG' || item['videoId'] != null) {
            quickPicksSongs.add(item);
            if (quickPicksSongs.length >= 12) break; // Increased from 6 to 12
          }
        }
      }

      // Extract Recently Played
      else if (sectionTitle.contains('recent') ||
               sectionTitle.contains('history') ||
               sectionTitle.contains('played')) {
        for (var item in contents) {
          if (item['type'] == 'SONG' || item['videoId'] != null) {
            recentlyPlayed.add(item);
            if (recentlyPlayed.length >= 8) break;
          }
        }
      }

      // Extract New Releases
      else if (sectionTitle.contains('new') ||
               sectionTitle.contains('release') ||
               sectionTitle.contains('latest')) {
        for (var item in contents) {
          if (item['type'] == 'ALBUM' || item['type'] == 'SONG') {
            newReleases.add(item);
            if (newReleases.length >= 10) break;
          }
        }
      }

      // Extract Chart Hits
      else if (sectionTitle.contains('chart') ||
               sectionTitle.contains('top') ||
               sectionTitle.contains('trending') ||
               sectionTitle.contains('popular')) {
        for (var item in contents) {
          if (item['type'] == 'SONG' || item['videoId'] != null) {
            chartHits.add(item);
            if (chartHits.length >= 10) break;
          }
        }
      }

      // Extract Albums (new releases, recommended albums) - Increased limit
      else if (sectionTitle.contains('album') ||
               sectionTitle.contains('discover')) {
        for (var item in contents) {
          if (item['type'] == 'ALBUM' || item['type'] == 'PLAYLIST') {
            recommendedAlbums.add(item);
            if (recommendedAlbums.length >= 15) break; // Increased from 10 to 15
          }
        }
      }

      // Extract Videos (music videos, trending) - Increased limit
      else if (sectionTitle.contains('video') ||
               sectionTitle.contains('music video')) {
        for (var item in contents) {
          if (item['type'] == 'VIDEO' || item['videoId'] != null) {
            trendingVideos.add(item);
            if (trendingVideos.length >= 12) break; // Increased from 8 to 12
          }
        }
      }

      // Extract Mood-based Playlists
      else if (sectionTitle.contains('mood') ||
               sectionTitle.contains('chill') ||
               sectionTitle.contains('relax') ||
               sectionTitle.contains('calm')) {
        for (var item in contents) {
          if (item['type'] == 'PLAYLIST') {
            moodPlaylists.add(item);
            if (moodPlaylists.length >= 8) break;
          }
        }
      }

      // Extract Genre-based Recommendations
      else if (sectionTitle.contains('genre') ||
               sectionTitle.contains('rock') ||
               sectionTitle.contains('pop') ||
               sectionTitle.contains('hip hop') ||
               sectionTitle.contains('electronic') ||
               sectionTitle.contains('jazz') ||
               sectionTitle.contains('classical')) {
        for (var item in contents) {
          if (item['type'] == 'PLAYLIST' || item['type'] == 'ALBUM') {
            genreRecommendations.add(item);
            if (genreRecommendations.length >= 10) break;
          }
        }
      }

      // Extract Artist Radio
      else if (sectionTitle.contains('radio') ||
               sectionTitle.contains('station') ||
               sectionTitle.contains('artist')) {
        for (var item in contents) {
          if (item['type'] == 'PLAYLIST' || item['type'] == 'RADIO') {
            artistRadio.add(item);
            if (artistRadio.length >= 8) break;
          }
        }
      }

      // Extract General Playlists - Increased limit
      else if (sectionTitle.contains('playlist')) {
        for (var item in contents) {
          if (item['type'] == 'PLAYLIST') {
            personalizedPlaylists.add(item);
            if (personalizedPlaylists.length >= 10) break; // Increased from 6 to 10
          }
        }
      }
    }

    // If we don't have enough items, fill from any available content with higher limits
    if (quickPicksSongs.length < 12) {
      for (var section in sections) {
        if (section['contents'] != null) {
          for (var item in section['contents']) {
            if ((item['type'] == 'SONG' || item['videoId'] != null) &&
                !quickPicksSongs.contains(item)) {
              quickPicksSongs.add(item);
              if (quickPicksSongs.length >= 12) break;
            }
          }
          if (quickPicksSongs.length >= 12) break;
        }
      }
    }

    // Fill albums if needed - increased limit
    if (recommendedAlbums.length < 15) {
      for (var section in sections) {
        if (section['contents'] != null) {
          for (var item in section['contents']) {
            if ((item['type'] == 'ALBUM' || item['type'] == 'PLAYLIST') &&
                !recommendedAlbums.contains(item)) {
              recommendedAlbums.add(item);
              if (recommendedAlbums.length >= 15) break;
            }
          }
          if (recommendedAlbums.length >= 15) break;
        }
      }
    }

    // Fill videos if needed - increased limit
    if (trendingVideos.length < 12) {
      for (var section in sections) {
        if (section['contents'] != null) {
          for (var item in section['contents']) {
            if ((item['type'] == 'VIDEO' || item['videoId'] != null) &&
                !trendingVideos.contains(item)) {
              trendingVideos.add(item);
              if (trendingVideos.length >= 12) break;
            }
          }
          if (trendingVideos.length >= 12) break;
        }
      }
    }

    // Fill new categories with fallback content
    _fillCategoryFallbacks(sections);
  }

  void _fillCategoryFallbacks(List sections) {
    // Fill mood playlists
    if (moodPlaylists.length < 8) {
      for (var section in sections) {
        if (section['contents'] != null) {
          for (var item in section['contents']) {
            if (item['type'] == 'PLAYLIST' && !moodPlaylists.contains(item)) {
              moodPlaylists.add(item);
              if (moodPlaylists.length >= 8) break;
            }
          }
          if (moodPlaylists.length >= 8) break;
        }
      }
    }

    // Fill genre recommendations
    if (genreRecommendations.length < 10) {
      for (var section in sections) {
        if (section['contents'] != null) {
          for (var item in section['contents']) {
            if ((item['type'] == 'PLAYLIST' || item['type'] == 'ALBUM') &&
                !genreRecommendations.contains(item)) {
              genreRecommendations.add(item);
              if (genreRecommendations.length >= 10) break;
            }
          }
          if (genreRecommendations.length >= 10) break;
        }
      }
    }

    // Fill new releases
    if (newReleases.length < 10) {
      for (var section in sections) {
        if (section['contents'] != null) {
          for (var item in section['contents']) {
            if ((item['type'] == 'ALBUM' || item['type'] == 'SONG') &&
                !newReleases.contains(item)) {
              newReleases.add(item);
              if (newReleases.length >= 10) break;
            }
          }
          if (newReleases.length >= 10) break;
        }
      }
    }

    // Fill chart hits
    if (chartHits.length < 10) {
      for (var section in sections) {
        if (section['contents'] != null) {
          for (var item in section['contents']) {
            if ((item['type'] == 'SONG' || item['videoId'] != null) &&
                !chartHits.contains(item)) {
              chartHits.add(item);
              if (chartHits.length >= 10) break;
            }
          }
          if (chartHits.length >= 10) break;
        }
      }
    }

    // Fill artist radio
    if (artistRadio.length < 8) {
      for (var section in sections) {
        if (section['contents'] != null) {
          for (var item in section['contents']) {
            if (item['type'] == 'PLAYLIST' && !artistRadio.contains(item)) {
              artistRadio.add(item);
              if (artistRadio.length >= 8) break;
            }
          }
          if (artistRadio.length >= 8) break;
        }
      }
    }
  }

  refresh() async {
    if (initialLoading) return;
    Map<String, dynamic> home = await ytMusic.browse();

    // Extract music recommendations on refresh
    await _extractMusicRecommendations(home['sections'] ?? []);

    if (mounted) {
      setState(() {
        initialLoading = false;
        nextLoading = false;
        chips = home['chips'];
        sections = home['sections'];
        continuation = home['continuation'];
      });
    }
  }

  Widget _horizontalChipsRow(List data) {
    return Container(
      height: 50,
      margin: const EdgeInsets.symmetric(vertical: 8),
      child: ListView.separated(
        scrollDirection: Axis.horizontal,
        padding: const EdgeInsets.symmetric(horizontal: 16),
        itemCount: data.length,
        separatorBuilder: (context, index) => const SizedBox(width: 12),
        itemBuilder: (context, index) {
          final element = data[index];
          return IOSStyleChip(
            text: element['title'] ?? '',
            onTap: () => context.go('/chip', extra: element),
          );
        },
      ),
    );
  }

  Widget _buildQuickPicksSection() {
    if (quickPicksSongs.isEmpty) {
      return const SizedBox.shrink();
    }

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Padding(
          padding: const EdgeInsets.symmetric(horizontal: 16),
          child: Row(
            children: [
              Text(
                'Quick picks',
                style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                  fontWeight: FontWeight.bold,
                  color: Colors.white,
                ),
              ),
              const Spacer(),
              Text(
                'Based on your listening',
                style: Theme.of(context).textTheme.bodySmall?.copyWith(
                  color: Colors.white70,
                ),
              ),
            ],
          ),
        ),
        const SizedBox(height: 16),
        Padding(
          padding: const EdgeInsets.symmetric(horizontal: 16),
          child: GridView.builder(
            shrinkWrap: true,
            physics: const NeverScrollableScrollPhysics(),
            gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
              crossAxisCount: 2,
              crossAxisSpacing: 12,
              mainAxisSpacing: 12,
              childAspectRatio: 3.2,
            ),
            itemCount: quickPicksSongs.length > 12 ? 12 : quickPicksSongs.length,
            itemBuilder: (context, index) {
              final song = quickPicksSongs[index];
              return _buildQuickPickCard(song);
            },
          ),
        ),
      ],
    );
  }

  @override
  Widget build(BuildContext context) {
    return AdaptiveScaffold(
      appBar: PreferredSize(
        preferredSize: const AdaptiveAppBar().preferredSize,
        child: AdaptiveAppBar(
          automaticallyImplyLeading: false,
          title: Material(
            color: Colors.transparent,
            child: LayoutBuilder(builder: (context, constraints) {
              return Row(
                children: [
                  ConstrainedBox(
                    constraints: BoxConstraints(
                        maxWidth: constraints.maxWidth > 400
                            ? (400)
                            : constraints.maxWidth),
                    child: IOSStyleSearchBar(
                      hintText: S.of(context).Search_Gyawun,
                      readOnly: true,
                      onTap: () => context.go('/search'),
                    ),
                  ),
                ],
              );
            }),
          ),
          centerTitle: false,
        ),
      ),
      body: initialLoading
          ? const Center(child: AdaptiveProgressRing())
          : Container(
              decoration: BoxDecoration(
                gradient: LinearGradient(
                  begin: Alignment.topCenter,
                  end: Alignment.bottomCenter,
                  colors: [
                    Theme.of(context).scaffoldBackgroundColor,
                    Theme.of(context).scaffoldBackgroundColor.withValues(alpha: 0.95),
                  ],
                ),
              ),
              child: RefreshIndicator(
                onRefresh: () => refresh(),
                color: Theme.of(context).colorScheme.primary,
                backgroundColor: Theme.of(context).colorScheme.surface,
                child: SingleChildScrollView(
                  padding: const EdgeInsets.only(top: 8, bottom: 24),
                  controller: _scrollController,
                  physics: const BouncingScrollPhysics(),
                  child: SafeArea(
                    child: Column(
                      children: [
                        _horizontalChipsRow(chips),
                        const SizedBox(height: 8),
                        // Quick picks section - 2 column grid
                        _buildQuickPicksSection(),
                        const SizedBox(height: 24),

                        // Albums for you section - horizontal scroll
                        _buildAlbumsForYouSection(),
                        const SizedBox(height: 24),

                        // Top music videos section
                        _buildTopMusicVideosSection(),
                        const SizedBox(height: 24),

                        // New Releases section
                        _buildNewReleasesSection(),
                        const SizedBox(height: 24),

                        // Chart Hits section
                        _buildChartHitsSection(),
                        const SizedBox(height: 24),

                        // Mood Playlists section
                        _buildMoodPlaylistsSection(),
                        const SizedBox(height: 24),

                        // Genre Recommendations section
                        _buildGenreRecommendationsSection(),
                        const SizedBox(height: 24),

                        // Artist Radio section
                        _buildArtistRadioSection(),
                        const SizedBox(height: 24),

                        // Dynamic sections from API
                        ...sections.asMap().entries.map((entry) {
                          final index = entry.key;
                          final section = entry.value;
                          return IOSStyleAnimatedContainer(
                            duration: Duration(milliseconds: 300 + (index * 100)),
                            child: Padding(
                              padding: const EdgeInsets.only(bottom: 16),
                              child: SectionItem(section: section),
                            ),
                          );
                        }),
                        if (!nextLoading && continuation != null)
                          const SizedBox(height: 50),
                        if (nextLoading)
                          Padding(
                            padding: const EdgeInsets.all(16.0),
                            child: Container(
                              padding: const EdgeInsets.all(16),
                              decoration: BoxDecoration(
                                color: Theme.of(context).colorScheme.surface.withValues(alpha: 0.8),
                                borderRadius: BorderRadius.circular(16),
                              ),
                              child: const AdaptiveProgressRing(),
                            ),
                          ),
                      ],
                    ),
                  ),
                ),
              ),
            ),
    );
  }

  Widget _buildQuickPickCard(Map song) {
    return GestureDetector(
      onTap: () async {
        // Play the song using MediaPlayer
        try {
          await GetIt.I<MediaPlayer>().playSong(Map<String, dynamic>.from(song));
        } catch (e) {
          // Handle error silently or show snackbar
          debugPrint('Error playing song: $e');
        }
      },
      child: Container(
        decoration: BoxDecoration(
          color: const Color(0xFF2A2A2A),
          borderRadius: BorderRadius.circular(8),
        ),
        child: Row(
        children: [
          ClipRRect(
            borderRadius: const BorderRadius.only(
              topLeft: Radius.circular(8),
              bottomLeft: Radius.circular(8),
            ),
            child: Container(
              width: 60,
              height: 60,
              color: Colors.grey[800],
              child: song['thumbnails'] != null && song['thumbnails'].isNotEmpty
                  ? CachedNetworkImage(
                      imageUrl: song['thumbnails'][0]['url'],
                      fit: BoxFit.cover,
                      placeholder: (context, url) => const Icon(Icons.music_note, color: Colors.white54),
                      errorWidget: (context, url, error) =>
                          const Icon(Icons.music_note, color: Colors.white54),
                    )
                  : const Icon(Icons.music_note, color: Colors.white54),
            ),
          ),
          Expanded(
            child: Padding(
              padding: const EdgeInsets.symmetric(horizontal: 12),
              child: Text(
                song['title'] ?? 'Unknown',
                style: const TextStyle(
                  color: Colors.white,
                  fontSize: 14,
                  fontWeight: FontWeight.w500,
                ),
                maxLines: 2,
                overflow: TextOverflow.ellipsis,
              ),
            ),
          ),
        ],
        ),
      ),
    );
  }

  Widget _buildAlbumsForYouSection() {
    if (recommendedAlbums.isEmpty) {
      return const SizedBox.shrink();
    }

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Padding(
          padding: const EdgeInsets.symmetric(horizontal: 16),
          child: Row(
            children: [
              Text(
                'Albums for you',
                style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                  fontWeight: FontWeight.bold,
                  color: Colors.white,
                ),
              ),
              const Spacer(),
              Text(
                'New releases & recommendations',
                style: Theme.of(context).textTheme.bodySmall?.copyWith(
                  color: Colors.white70,
                ),
              ),
            ],
          ),
        ),
        const SizedBox(height: 16),
        SizedBox(
          height: 200,
          child: ListView.builder(
            scrollDirection: Axis.horizontal,
            padding: const EdgeInsets.symmetric(horizontal: 16),
            itemCount: recommendedAlbums.length,
            itemBuilder: (context, index) {
              final album = recommendedAlbums[index];
              return _buildAlbumCard(album);
            },
          ),
        ),
      ],
    );
  }

  Widget _buildAlbumCard(Map album) {
    return GestureDetector(
      onTap: () async {
        try {
          // Handle different types of content
          if (album['endpoint'] != null) {
            // For albums/playlists, start playing the songs
            await GetIt.I<MediaPlayer>().startPlaylistSongs(album['endpoint']);
          } else if (album['playlistId'] != null) {
            // Play playlist directly using endpoint format
            Map<String, dynamic> endpoint = {'playlistId': album['playlistId']};
            await GetIt.I<MediaPlayer>().startPlaylistSongs(endpoint);
          } else if (album['videoId'] != null) {
            // Play single song
            await GetIt.I<MediaPlayer>().playSong(Map<String, dynamic>.from(album));
          } else {
            // Fallback: navigate to browse page
            context.go('/browse', extra: album['endpoint'] ?? {});
          }
        } catch (e) {
          debugPrint('Error playing album/playlist: $e');
          // Fallback to navigation if playback fails
          if (album['endpoint'] != null && mounted) {
            context.go('/browse', extra: album['endpoint']);
          }
        }
      },
      child: Container(
        width: 140,
        margin: const EdgeInsets.only(right: 16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            ClipRRect(
              borderRadius: BorderRadius.circular(8),
              child: Container(
                width: 140,
                height: 140,
                color: Colors.grey[800],
                child: Stack(
                  children: [
                    album['thumbnails'] != null && album['thumbnails'].isNotEmpty
                        ? CachedNetworkImage(
                            imageUrl: album['thumbnails'][0]['url'],
                            fit: BoxFit.cover,
                            width: 140,
                            height: 140,
                            placeholder: (context, url) => const Icon(Icons.album, color: Colors.white54, size: 40),
                            errorWidget: (context, url, error) =>
                                const Icon(Icons.album, color: Colors.white54, size: 40),
                          )
                        : const Icon(Icons.album, color: Colors.white54, size: 40),
                    // Play button overlay
                    Positioned(
                      bottom: 8,
                      right: 8,
                      child: Container(
                        width: 32,
                        height: 32,
                        decoration: BoxDecoration(
                          color: Colors.white,
                          borderRadius: BorderRadius.circular(16),
                          boxShadow: [
                            BoxShadow(
                              color: Colors.black.withValues(alpha: 0.3),
                              blurRadius: 4,
                              offset: const Offset(0, 2),
                            ),
                          ],
                        ),
                        child: const Icon(
                          Icons.play_arrow,
                          color: Colors.black,
                          size: 20,
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            ),
          const SizedBox(height: 8),
          Text(
            album['title'] ?? 'Unknown Album',
            style: const TextStyle(
              color: Colors.white,
              fontSize: 14,
              fontWeight: FontWeight.w500,
            ),
            maxLines: 2,
            overflow: TextOverflow.ellipsis,
          ),
          if (album['subtitle'] != null)
            Text(
              album['subtitle'],
              style: const TextStyle(
                color: Colors.white70,
                fontSize: 12,
              ),
              maxLines: 1,
              overflow: TextOverflow.ellipsis,
            ),
        ],
      ),
    ),
    );
  }

  Widget _buildTopMusicVideosSection() {
    if (trendingVideos.isEmpty) {
      return const SizedBox.shrink();
    }

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Padding(
          padding: const EdgeInsets.symmetric(horizontal: 16),
          child: Row(
            children: [
              Text(
                'Top music videos',
                style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                  fontWeight: FontWeight.bold,
                  color: Colors.white,
                ),
              ),
              const Spacer(),
              Text(
                'Trending now',
                style: Theme.of(context).textTheme.bodySmall?.copyWith(
                  color: Colors.white70,
                ),
              ),
            ],
          ),
        ),
        const SizedBox(height: 16),
        SizedBox(
          height: 120,
          child: ListView.builder(
            scrollDirection: Axis.horizontal,
            padding: const EdgeInsets.symmetric(horizontal: 16),
            itemCount: trendingVideos.length,
            itemBuilder: (context, index) {
              final video = trendingVideos[index];
              return _buildVideoCard(video);
            },
          ),
        ),
      ],
    );
  }

  Widget _buildVideoCard(Map video) {
    return Container(
      width: 160,
      margin: const EdgeInsets.only(right: 16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          ClipRRect(
            borderRadius: BorderRadius.circular(8),
            child: Container(
              width: 160,
              height: 90,
              color: Colors.grey[800],
              child: Stack(
                children: [
                  if (video['thumbnails'] != null && video['thumbnails'].isNotEmpty)
                    CachedNetworkImage(
                      imageUrl: video['thumbnails'][0]['url'],
                      width: 160,
                      height: 90,
                      fit: BoxFit.cover,
                      placeholder: (context, url) => const Icon(Icons.video_library, color: Colors.white54, size: 30),
                      errorWidget: (context, url, error) =>
                          const Icon(Icons.video_library, color: Colors.white54, size: 30),
                    )
                  else
                    const Icon(Icons.video_library, color: Colors.white54, size: 30),
                  const Center(
                    child: Icon(
                      Icons.play_circle_filled,
                      color: Colors.white,
                      size: 40,
                    ),
                  ),
                ],
              ),
            ),
          ),
          const SizedBox(height: 8),
          Text(
            video['title'] ?? 'Unknown Video',
            style: const TextStyle(
              color: Colors.white,
              fontSize: 12,
              fontWeight: FontWeight.w500,
            ),
            maxLines: 2,
            overflow: TextOverflow.ellipsis,
          ),
        ],
      ),
    );
  }

  Widget _buildNewReleasesSection() {
    if (newReleases.isEmpty) {
      return const SizedBox.shrink();
    }

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Padding(
          padding: const EdgeInsets.symmetric(horizontal: 16),
          child: Row(
            children: [
              Text(
                'New Releases',
                style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                  fontWeight: FontWeight.bold,
                  color: Colors.white,
                ),
              ),
              const Spacer(),
              Text(
                'Fresh music just for you',
                style: Theme.of(context).textTheme.bodySmall?.copyWith(
                  color: Colors.white70,
                ),
              ),
            ],
          ),
        ),
        const SizedBox(height: 16),
        SizedBox(
          height: 200,
          child: ListView.builder(
            scrollDirection: Axis.horizontal,
            padding: const EdgeInsets.symmetric(horizontal: 16),
            itemCount: newReleases.length,
            itemBuilder: (context, index) {
              final item = newReleases[index];
              return _buildAlbumCard(item);
            },
          ),
        ),
      ],
    );
  }

  Widget _buildChartHitsSection() {
    if (chartHits.isEmpty) {
      return const SizedBox.shrink();
    }

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Padding(
          padding: const EdgeInsets.symmetric(horizontal: 16),
          child: Row(
            children: [
              Text(
                'Chart Hits',
                style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                  fontWeight: FontWeight.bold,
                  color: Colors.white,
                ),
              ),
              const Spacer(),
              Text(
                'What\'s trending now',
                style: Theme.of(context).textTheme.bodySmall?.copyWith(
                  color: Colors.white70,
                ),
              ),
            ],
          ),
        ),
        const SizedBox(height: 16),
        Padding(
          padding: const EdgeInsets.symmetric(horizontal: 16),
          child: GridView.builder(
            shrinkWrap: true,
            physics: const NeverScrollableScrollPhysics(),
            gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
              crossAxisCount: 2,
              crossAxisSpacing: 12,
              mainAxisSpacing: 12,
              childAspectRatio: 3.2,
            ),
            itemCount: chartHits.length > 8 ? 8 : chartHits.length,
            itemBuilder: (context, index) {
              final song = chartHits[index];
              return _buildQuickPickCard(song);
            },
          ),
        ),
      ],
    );
  }

  Widget _buildMoodPlaylistsSection() {
    if (moodPlaylists.isEmpty) {
      return const SizedBox.shrink();
    }

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Padding(
          padding: const EdgeInsets.symmetric(horizontal: 16),
          child: Row(
            children: [
              Text(
                'Mood & Activity',
                style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                  fontWeight: FontWeight.bold,
                  color: Colors.white,
                ),
              ),
              const Spacer(),
              Text(
                'Music for every moment',
                style: Theme.of(context).textTheme.bodySmall?.copyWith(
                  color: Colors.white70,
                ),
              ),
            ],
          ),
        ),
        const SizedBox(height: 16),
        SizedBox(
          height: 200,
          child: ListView.builder(
            scrollDirection: Axis.horizontal,
            padding: const EdgeInsets.symmetric(horizontal: 16),
            itemCount: moodPlaylists.length,
            itemBuilder: (context, index) {
              final playlist = moodPlaylists[index];
              return _buildAlbumCard(playlist);
            },
          ),
        ),
      ],
    );
  }

  Widget _buildGenreRecommendationsSection() {
    if (genreRecommendations.isEmpty) {
      return const SizedBox.shrink();
    }

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Padding(
          padding: const EdgeInsets.symmetric(horizontal: 16),
          child: Row(
            children: [
              Text(
                'Explore Genres',
                style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                  fontWeight: FontWeight.bold,
                  color: Colors.white,
                ),
              ),
              const Spacer(),
              Text(
                'Discover new sounds',
                style: Theme.of(context).textTheme.bodySmall?.copyWith(
                  color: Colors.white70,
                ),
              ),
            ],
          ),
        ),
        const SizedBox(height: 16),
        SizedBox(
          height: 200,
          child: ListView.builder(
            scrollDirection: Axis.horizontal,
            padding: const EdgeInsets.symmetric(horizontal: 16),
            itemCount: genreRecommendations.length,
            itemBuilder: (context, index) {
              final item = genreRecommendations[index];
              return _buildAlbumCard(item);
            },
          ),
        ),
      ],
    );
  }

  Widget _buildArtistRadioSection() {
    if (artistRadio.isEmpty) {
      return const SizedBox.shrink();
    }

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Padding(
          padding: const EdgeInsets.symmetric(horizontal: 16),
          child: Row(
            children: [
              Text(
                'Artist Radio',
                style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                  fontWeight: FontWeight.bold,
                  color: Colors.white,
                ),
              ),
              const Spacer(),
              Text(
                'Based on your favorites',
                style: Theme.of(context).textTheme.bodySmall?.copyWith(
                  color: Colors.white70,
                ),
              ),
            ],
          ),
        ),
        const SizedBox(height: 16),
        SizedBox(
          height: 200,
          child: ListView.builder(
            scrollDirection: Axis.horizontal,
            padding: const EdgeInsets.symmetric(horizontal: 16),
            itemCount: artistRadio.length,
            itemBuilder: (context, index) {
              final radio = artistRadio[index];
              return _buildAlbumCard(radio);
            },
          ),
        ),
      ],
    );
  }
}
