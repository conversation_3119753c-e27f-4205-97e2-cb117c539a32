import 'dart:async';
import 'dart:io';
import 'dart:isolate';
import 'package:device_info_plus/device_info_plus.dart';
import 'package:fluent_ui/fluent_ui.dart' as fluent_ui;
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:receive_sharing_intent/receive_sharing_intent.dart';
import 'dart:ui';



import 'package:window_manager/window_manager.dart';


import '../../themes/colors.dart';
import '../../utils/adaptive_widgets/adaptive_widgets.dart';
import '../../utils/bottom_modals.dart';
import '../../utils/check_update.dart';
import '../browse_screen/browse_screen.dart';
import 'new_bottom_player.dart';

class MainScreen extends StatefulWidget {
  const MainScreen({
    Key? key,
    required this.navigationShell,
  }) : super(key: key ?? const ValueKey('MainScreen'));
  final StatefulNavigationShell navigationShell;

  @override
  State<MainScreen> createState() => _MainScreenState();
}

class _MainScreenState extends State<MainScreen> with WindowListener {
  late StreamSubscription _intentSub;
  @override
  void initState() {
    windowManager.addListener(this);
    super.initState();
    if (Platform.isAndroid) {
      _intentSub =
          ReceiveSharingIntent.instance.getMediaStream().listen((value) {
        if (value.isNotEmpty) _handleIntent(value.first);
      });

      ReceiveSharingIntent.instance.getInitialMedia().then((value) {
        if (value.isNotEmpty) _handleIntent(value.first);
        ReceiveSharingIntent.instance.reset();
      });
    }

    _update();
  }

  _handleIntent(SharedMediaFile value) {
    if (value.mimeType == 'text/plain' &&
        value.path.contains('music.youtube.com')) {
      Uri? uri = Uri.tryParse(value.path);
      if (uri != null) {
        if (uri.pathSegments.first == 'watch' &&
            uri.queryParameters['v'] != null) {
          context.push('/player', extra: uri.queryParameters['v']);
        } else if (uri.pathSegments.first == 'playlist' &&
            uri.queryParameters['list'] != null) {
          String id = uri.queryParameters['list']!;
          Navigator.push(
            context,
            AdaptivePageRoute.create(
              (_) => BrowseScreen(
                  endpoint: {'browseId': id.startsWith('VL') ? id : 'VL$id'}),
            ),
          );
        }
      }
    }
  }

  @override
  void dispose() {
    windowManager.removeListener(this);
    _intentSub.cancel();
    super.dispose();
  }

  _update() async {
    final deviceInfoPlugin = DeviceInfoPlugin();
    BaseDeviceInfo deviceInfo = await deviceInfoPlugin.deviceInfo;
    UpdateInfo? updateInfo = await Isolate.run(() async {
      return await checkUpdate(deviceInfo: deviceInfo);
    });

    if (updateInfo != null) {
      if (mounted) {
        Modals.showUpdateDialog(context, updateInfo);
      }
    }
  }

  void _goBranch(int index) {
    widget.navigationShell.goBranch(
      index,
      initialLocation: index == widget.navigationShell.currentIndex,
    );
  }

  @override
  void onWindowClose() async {
    windowManager.destroy();
  }

  @override
  Widget build(BuildContext context) {
    return Platform.isWindows
        ? _buildWindowsMain(
            _goBranch,
            widget.navigationShell,
          )
        : Scaffold(
            body: Column(
              children: [
                Expanded(
                  child: SafeArea(
                    bottom: false,
                    child: widget.navigationShell,
                  ),
                ),
                const NewBottomPlayer()
              ],
            ),
            bottomNavigationBar: SafeArea(
              child: Container(
                height: 80,
                margin: const EdgeInsets.only(bottom: 12),
                decoration: BoxDecoration(
                  color: Theme.of(context).brightness == Brightness.dark
                      ? Colors.grey[900]?.withValues(alpha: 0.6)
                      : Colors.grey[100]?.withValues(alpha: 0.8),
                  borderRadius: const BorderRadius.only(
                    topLeft: Radius.circular(12),
                    topRight: Radius.circular(12),
                  ),
                  border: Border(
                    top: BorderSide(
                      color: Theme.of(context).brightness == Brightness.dark
                          ? Colors.white.withValues(alpha: 0.1)
                          : Colors.black.withValues(alpha: 0.05),
                      width: 1.0,
                    ),
                  ),
                  boxShadow: [
                    BoxShadow(
                      color: Theme.of(context).brightness == Brightness.dark
                          ? Colors.black.withValues(alpha: 0.6)
                          : Colors.black.withValues(alpha: 0.1),
                      blurRadius: 8,
                      offset: const Offset(0, -4),
                    ),
                  ],
                ),
              child: ClipRRect(
                borderRadius: const BorderRadius.only(
                  topLeft: Radius.circular(iOSBorderRadius),
                  topRight: Radius.circular(iOSBorderRadius),
                ),
                child: BackdropFilter(
                  filter: ImageFilter.blur(sigmaX: 20, sigmaY: 20),
                  child: Container(
                    decoration: BoxDecoration(
                      gradient: LinearGradient(
                        begin: Alignment.topCenter,
                        end: Alignment.bottomCenter,
                        colors: [
                          Colors.white.withValues(alpha: 0.9),
                          Colors.white.withValues(alpha: 0.95),
                        ],
                      ),
                      border: Border(
                        top: BorderSide(
                          color: Colors.black.withValues(alpha: 0.1),
                          width: 0.5,
                        ),
                      ),
                    ),
                    child: CupertinoTabBar(
                      backgroundColor: Colors.transparent,
                      border: null,
                      currentIndex: widget.navigationShell.currentIndex,
                      onTap: _goBranch,
                      activeColor: const Color(0xFF007AFF),
                      inactiveColor: Colors.black.withValues(alpha: 0.5),
                      iconSize: 26,
                      height: 65,
                      items: const [
                        BottomNavigationBarItem(
                          icon: Icon(CupertinoIcons.house),
                          activeIcon: Icon(CupertinoIcons.house_fill),
                          label: 'Home',
                        ),
                        BottomNavigationBarItem(
                          icon: Icon(CupertinoIcons.music_albums),
                          activeIcon: Icon(CupertinoIcons.music_albums_fill),
                          label: 'Library',
                        ),
                        BottomNavigationBarItem(
                          icon: Icon(CupertinoIcons.gear),
                          activeIcon: Icon(CupertinoIcons.gear_solid),
                          label: 'Settings',
                        ),
                      ],
                    ),
                  ),
                ),
              ),
              ),
            ),
          );
  }

  _buildWindowsMain(
      Function goTOBranch, StatefulNavigationShell navigationShell) {
    return Directionality(
      textDirection: fluent_ui.TextDirection.ltr,
      child: fluent_ui.NavigationView(
        appBar: fluent_ui.NavigationAppBar(
          title: DragToMoveArea(
            child: Align(
              alignment: AlignmentDirectional.centerStart,
              child: Text("CODO"),
            ),
          ),
          leading: fluent_ui.Padding(
            padding: const EdgeInsets.symmetric(horizontal: 12),
            child: Image.asset(
              'assets/images/icon.png',
              height: 25,
              width: 25,
            ),
          ),
          actions: const WindowButtons(),
        ),
        paneBodyBuilder: (item, body) {
          return Column(
            children: [
              fluent_ui.Expanded(child: navigationShell),
              const NewBottomPlayer()
            ],
          );
        },
        pane: fluent_ui.NavigationPane(
            selected: widget.navigationShell.currentIndex,
            size: const fluent_ui.NavigationPaneSize(
              compactWidth: 60,
            ),
            items: [
              fluent_ui.PaneItem(
                key: const ValueKey('/'),
                icon: const Icon(
                  fluent_ui.FluentIcons.home_solid,
                  size: 30,
                ),
                title: Text("Home"),
                body: const SizedBox.shrink(),
                onTap: () => goTOBranch(0),
              ),
              fluent_ui.PaneItem(
                key: const ValueKey('/saved'),
                icon: const Icon(
                  fluent_ui.FluentIcons.library,
                  size: 30,
                ),
                title: Text("Saved"),
                body: const SizedBox.shrink(),
                onTap: () => goTOBranch(1),
              ),
            ],
            footerItems: [
              fluent_ui.PaneItem(
                key: const ValueKey('/settings'),
                icon: const Icon(
                  fluent_ui.FluentIcons.settings,
                  size: 30,
                ),
                title: Text("Settings"),
                body: const SizedBox.shrink(),
                onTap: () => goTOBranch(2),
              )
            ]),
      ),
    );
  }
}

class WindowButtons extends StatelessWidget {
  const WindowButtons({super.key});

  @override
  Widget build(BuildContext context) {
    final fluent_ui.FluentThemeData theme = fluent_ui.FluentTheme.of(context);

    return SizedBox(
      width: 138,
      height: 50,
      child: WindowCaption(
        brightness: theme.brightness,
        backgroundColor: Colors.transparent,
      ),
    );
  }
}
