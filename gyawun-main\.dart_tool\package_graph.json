{"roots": ["g<PERSON><PERSON><PERSON>"], "packages": [{"name": "g<PERSON><PERSON><PERSON>", "version": "2.0.13+38", "dependencies": ["audio_video_progress_bar", "audiotags", "cached_network_image", "country_picker", "cupertino_icons", "device_info_plus", "duration_picker", "dynamic_color", "easy_folder_picker", "expandable_page_view", "expandable_text", "file_picker", "fl_toast", "fluent_ui", "flutter", "flutter_acrylic", "flutter_colorpicker", "flutter_expandable_fab", "flutter_localizations", "flutter_lyric", "flutter_markdown", "flutter_staggered_grid_view", "flutter_swipe_action_cell", "flutter_typeahead", "get_it", "go_router", "google_fonts", "hive", "hive_flutter", "http", "image_picker", "intl", "just_audio", "just_audio_background", "just_audio_media_kit", "media_kit_libs_linux", "media_kit_libs_windows_audio", "media_kit_native_event_loop", "palette_generator", "path", "path_provider", "permission_handler", "provider", "pub_semver", "receive_sharing_intent", "salomon_bottom_bar", "share_plus", "shared_preferences", "sliding_up_panel", "text_scroll", "url_launcher", "window_manager", "yaml", "youtube_explode_dart"], "devDependencies": ["change_app_package_name", "flutter_lints", "flutter_native_splash", "flutter_test"]}, {"name": "flutter_native_splash", "version": "2.4.6", "dependencies": ["ansicolor", "args", "flutter", "flutter_web_plugins", "html", "image", "meta", "path", "universal_io", "xml", "yaml"]}, {"name": "change_app_package_name", "version": "1.5.0", "dependencies": []}, {"name": "flutter_lints", "version": "5.0.0", "dependencies": ["lints"]}, {"name": "flutter_test", "version": "0.0.0", "dependencies": ["async", "boolean_selector", "characters", "clock", "collection", "fake_async", "flutter", "leak_tracker", "leak_tracker_flutter_testing", "leak_tracker_testing", "matcher", "material_color_utilities", "meta", "path", "source_span", "stack_trace", "stream_channel", "string_scanner", "term_glyph", "test_api", "vector_math", "vm_service"]}, {"name": "yaml", "version": "3.1.3", "dependencies": ["collection", "source_span", "string_scanner"]}, {"name": "shared_preferences", "version": "2.5.3", "dependencies": ["flutter", "shared_preferences_android", "shared_preferences_foundation", "shared_preferences_linux", "shared_preferences_platform_interface", "shared_preferences_web", "shared_preferences_windows"]}, {"name": "image_picker", "version": "1.1.2", "dependencies": ["flutter", "image_picker_android", "image_picker_for_web", "image_picker_ios", "image_picker_linux", "image_picker_macos", "image_picker_platform_interface", "image_picker_windows"]}, {"name": "permission_handler", "version": "11.4.0", "dependencies": ["flutter", "meta", "permission_handler_android", "permission_handler_apple", "permission_handler_html", "permission_handler_platform_interface", "permission_handler_windows"]}, {"name": "easy_folder_picker", "version": "1.3.4", "dependencies": ["flutter", "path", "permission_handler"]}, {"name": "flutter_colorpicker", "version": "1.1.0", "dependencies": ["flutter"]}, {"name": "fl_toast", "version": "3.2.0", "dependencies": ["flutter"]}, {"name": "receive_sharing_intent", "version": "1.8.0", "dependencies": ["flutter", "plugin_platform_interface"]}, {"name": "window_manager", "version": "0.4.3", "dependencies": ["flutter", "path", "screen_retriever"]}, {"name": "fluent_ui", "version": "4.11.4", "dependencies": ["flutter", "flutter_localizations", "intl", "math_expressions", "recase", "scroll_pos"]}, {"name": "flutter_acrylic", "version": "1.1.4", "dependencies": ["flutter", "macos_window_utils"]}, {"name": "media_kit_native_event_loop", "version": "1.0.9", "dependencies": ["flutter"]}, {"name": "just_audio_media_kit", "version": "2.1.0", "dependencies": ["flutter", "just_audio_platform_interface", "logging", "media_kit", "universal_platform"]}, {"name": "media_kit_libs_linux", "version": "1.2.1", "dependencies": ["flutter"]}, {"name": "media_kit_libs_windows_audio", "version": "1.0.9", "dependencies": ["flutter"]}, {"name": "flutter_lyric", "version": "2.0.4+6", "dependencies": ["collection", "flutter"]}, {"name": "flutter_expandable_fab", "version": "2.5.1", "dependencies": ["flutter"]}, {"name": "flutter_markdown", "version": "0.7.7+1", "dependencies": ["flutter", "markdown", "meta", "path"]}, {"name": "device_info_plus", "version": "11.4.0", "dependencies": ["device_info_plus_platform_interface", "ffi", "file", "flutter", "flutter_web_plugins", "meta", "web", "win32", "win32_registry"]}, {"name": "pub_semver", "version": "2.2.0", "dependencies": ["collection"]}, {"name": "audiotags", "version": "1.4.5", "dependencies": ["ffi", "flutter", "flutter_rust_bridge", "freezed_annotation", "meta", "plugin_platform_interface", "uuid"]}, {"name": "dynamic_color", "version": "1.7.0", "dependencies": ["flutter", "flutter_test", "material_color_utilities"]}, {"name": "file_picker", "version": "10.1.9", "dependencies": ["cross_file", "ffi", "flutter", "flutter_plugin_android_lifecycle", "flutter_web_plugins", "path", "plugin_platform_interface", "web", "win32"]}, {"name": "path", "version": "1.9.1", "dependencies": []}, {"name": "path_provider", "version": "2.1.5", "dependencies": ["flutter", "path_provider_android", "path_provider_foundation", "path_provider_linux", "path_provider_platform_interface", "path_provider_windows"]}, {"name": "duration_picker", "version": "1.2.0", "dependencies": ["flutter"]}, {"name": "text_scroll", "version": "0.2.0", "dependencies": ["flutter"]}, {"name": "palette_generator", "version": "0.3.3+7", "dependencies": ["collection", "flutter"]}, {"name": "country_picker", "version": "2.0.27", "dependencies": ["collection", "flutter", "universal_io"]}, {"name": "url_launcher", "version": "6.3.1", "dependencies": ["flutter", "url_launcher_android", "url_launcher_ios", "url_launcher_linux", "url_launcher_macos", "url_launcher_platform_interface", "url_launcher_web", "url_launcher_windows"]}, {"name": "sliding_up_panel", "version": "2.0.0+1", "dependencies": ["flutter"]}, {"name": "audio_video_progress_bar", "version": "2.0.3", "dependencies": ["flutter"]}, {"name": "just_audio_background", "version": "0.0.1-beta.17", "dependencies": ["audio_service", "audio_session", "flutter", "flutter_web_plugins", "just_audio_platform_interface", "meta", "rxdart", "synchronized"]}, {"name": "just_audio", "version": "0.10.3", "dependencies": ["async", "audio_session", "crypto", "flutter", "just_audio_platform_interface", "just_audio_web", "meta", "path", "path_provider", "rxdart", "synchronized", "uuid"]}, {"name": "flutter_swipe_action_cell", "version": "3.1.5", "dependencies": ["flutter"]}, {"name": "flutter_staggered_grid_view", "version": "0.7.0", "dependencies": ["flutter"]}, {"name": "provider", "version": "6.1.5", "dependencies": ["collection", "flutter", "nested"]}, {"name": "share_plus", "version": "11.0.0", "dependencies": ["cross_file", "ffi", "file", "flutter", "flutter_web_plugins", "meta", "mime", "share_plus_platform_interface", "url_launcher_linux", "url_launcher_platform_interface", "url_launcher_web", "url_launcher_windows", "web", "win32"]}, {"name": "salomon_bottom_bar", "version": "3.3.2", "dependencies": ["flutter"]}, {"name": "expandable_text", "version": "2.3.0", "dependencies": ["flutter"]}, {"name": "flutter_typeahead", "version": "5.2.0", "dependencies": ["flutter", "flutter_keyboard_visibility", "pointer_interceptor"]}, {"name": "cached_network_image", "version": "3.4.1", "dependencies": ["cached_network_image_platform_interface", "cached_network_image_web", "flutter", "flutter_cache_manager", "octo_image"]}, {"name": "get_it", "version": "8.0.3", "dependencies": ["async", "collection", "meta"]}, {"name": "expandable_page_view", "version": "1.0.17", "dependencies": ["flutter"]}, {"name": "youtube_explode_dart", "version": "2.4.0-dev.1", "dependencies": ["collection", "freezed_annotation", "html", "http", "http_parser", "json_annotation", "logging", "meta", "unicode", "xml"]}, {"name": "hive_flutter", "version": "1.1.0", "dependencies": ["flutter", "hive", "path", "path_provider"]}, {"name": "hive", "version": "2.2.3", "dependencies": ["crypto", "meta"]}, {"name": "http", "version": "1.4.0", "dependencies": ["async", "http_parser", "meta", "web"]}, {"name": "intl", "version": "0.20.2", "dependencies": ["clock", "meta", "path"]}, {"name": "google_fonts", "version": "6.2.1", "dependencies": ["crypto", "flutter", "http", "path_provider"]}, {"name": "go_router", "version": "15.1.2", "dependencies": ["collection", "flutter", "flutter_web_plugins", "logging", "meta"]}, {"name": "cupertino_icons", "version": "1.0.8", "dependencies": []}, {"name": "flutter_localizations", "version": "0.0.0", "dependencies": ["characters", "clock", "collection", "flutter", "intl", "material_color_utilities", "meta", "path", "vector_math"]}, {"name": "flutter", "version": "0.0.0", "dependencies": ["characters", "collection", "material_color_utilities", "meta", "sky_engine", "vector_math"]}, {"name": "ansicolor", "version": "2.0.3", "dependencies": []}, {"name": "xml", "version": "6.5.0", "dependencies": ["collection", "meta", "petitparser"]}, {"name": "universal_io", "version": "2.2.2", "dependencies": ["collection", "meta", "typed_data"]}, {"name": "meta", "version": "1.16.0", "dependencies": []}, {"name": "image", "version": "4.5.4", "dependencies": ["archive", "meta", "xml"]}, {"name": "html", "version": "0.15.6", "dependencies": ["csslib", "source_span"]}, {"name": "flutter_web_plugins", "version": "0.0.0", "dependencies": ["characters", "collection", "flutter", "material_color_utilities", "meta", "vector_math"]}, {"name": "args", "version": "2.7.0", "dependencies": []}, {"name": "lints", "version": "5.1.1", "dependencies": []}, {"name": "vm_service", "version": "15.0.0", "dependencies": []}, {"name": "term_glyph", "version": "1.2.2", "dependencies": []}, {"name": "string_scanner", "version": "1.4.1", "dependencies": ["source_span"]}, {"name": "stream_channel", "version": "2.1.4", "dependencies": ["async"]}, {"name": "source_span", "version": "1.10.1", "dependencies": ["collection", "path", "term_glyph"]}, {"name": "material_color_utilities", "version": "0.11.1", "dependencies": ["collection"]}, {"name": "leak_tracker_testing", "version": "3.0.1", "dependencies": ["leak_tracker", "matcher", "meta"]}, {"name": "leak_tracker", "version": "10.0.9", "dependencies": ["clock", "collection", "meta", "path", "vm_service"]}, {"name": "collection", "version": "1.19.1", "dependencies": []}, {"name": "characters", "version": "1.4.0", "dependencies": []}, {"name": "boolean_selector", "version": "2.1.2", "dependencies": ["source_span", "string_scanner"]}, {"name": "async", "version": "2.13.0", "dependencies": ["collection", "meta"]}, {"name": "leak_tracker_flutter_testing", "version": "3.0.9", "dependencies": ["flutter", "leak_tracker", "leak_tracker_testing", "matcher", "meta"]}, {"name": "vector_math", "version": "2.1.4", "dependencies": []}, {"name": "stack_trace", "version": "1.12.1", "dependencies": ["path"]}, {"name": "clock", "version": "1.1.2", "dependencies": []}, {"name": "fake_async", "version": "1.3.3", "dependencies": ["clock", "collection"]}, {"name": "matcher", "version": "0.12.17", "dependencies": ["async", "meta", "stack_trace", "term_glyph", "test_api"]}, {"name": "test_api", "version": "0.7.4", "dependencies": ["async", "boolean_selector", "collection", "meta", "source_span", "stack_trace", "stream_channel", "string_scanner", "term_glyph"]}, {"name": "shared_preferences_windows", "version": "2.4.1", "dependencies": ["file", "flutter", "path", "path_provider_platform_interface", "path_provider_windows", "shared_preferences_platform_interface"]}, {"name": "shared_preferences_web", "version": "2.4.3", "dependencies": ["flutter", "flutter_web_plugins", "shared_preferences_platform_interface", "web"]}, {"name": "shared_preferences_platform_interface", "version": "2.4.1", "dependencies": ["flutter", "plugin_platform_interface"]}, {"name": "shared_preferences_linux", "version": "2.4.1", "dependencies": ["file", "flutter", "path", "path_provider_linux", "path_provider_platform_interface", "shared_preferences_platform_interface"]}, {"name": "shared_preferences_foundation", "version": "2.5.4", "dependencies": ["flutter", "shared_preferences_platform_interface"]}, {"name": "shared_preferences_android", "version": "2.4.10", "dependencies": ["flutter", "shared_preferences_platform_interface"]}, {"name": "image_picker_windows", "version": "0.2.1+1", "dependencies": ["file_selector_platform_interface", "file_selector_windows", "flutter", "image_picker_platform_interface"]}, {"name": "image_picker_platform_interface", "version": "2.10.1", "dependencies": ["cross_file", "flutter", "http", "plugin_platform_interface"]}, {"name": "image_picker_macos", "version": "0.2.1+2", "dependencies": ["file_selector_macos", "file_selector_platform_interface", "flutter", "image_picker_platform_interface"]}, {"name": "image_picker_linux", "version": "0.2.1+2", "dependencies": ["file_selector_linux", "file_selector_platform_interface", "flutter", "image_picker_platform_interface"]}, {"name": "image_picker_ios", "version": "0.8.12+2", "dependencies": ["flutter", "image_picker_platform_interface"]}, {"name": "image_picker_for_web", "version": "3.0.6", "dependencies": ["flutter", "flutter_web_plugins", "image_picker_platform_interface", "mime", "web"]}, {"name": "image_picker_android", "version": "0.8.12+23", "dependencies": ["flutter", "flutter_plugin_android_lifecycle", "image_picker_platform_interface"]}, {"name": "permission_handler_platform_interface", "version": "4.3.0", "dependencies": ["flutter", "meta", "plugin_platform_interface"]}, {"name": "permission_handler_windows", "version": "0.2.1", "dependencies": ["flutter", "permission_handler_platform_interface"]}, {"name": "permission_handler_html", "version": "0.1.3+5", "dependencies": ["flutter", "flutter_web_plugins", "permission_handler_platform_interface", "web"]}, {"name": "permission_handler_apple", "version": "9.4.7", "dependencies": ["flutter", "permission_handler_platform_interface"]}, {"name": "permission_handler_android", "version": "12.1.0", "dependencies": ["flutter", "permission_handler_platform_interface"]}, {"name": "plugin_platform_interface", "version": "2.1.8", "dependencies": ["meta"]}, {"name": "screen_retriever", "version": "0.2.0", "dependencies": ["flutter", "screen_retriever_linux", "screen_retriever_macos", "screen_retriever_platform_interface", "screen_retriever_windows"]}, {"name": "math_expressions", "version": "2.7.0", "dependencies": ["petitparser", "vector_math"]}, {"name": "recase", "version": "4.1.0", "dependencies": []}, {"name": "scroll_pos", "version": "0.5.0", "dependencies": ["flutter"]}, {"name": "macos_window_utils", "version": "1.7.1", "dependencies": ["flutter"]}, {"name": "universal_platform", "version": "1.1.0", "dependencies": []}, {"name": "media_kit", "version": "1.2.0", "dependencies": ["collection", "http", "image", "meta", "path", "safe_local_storage", "synchronized", "universal_platform", "uri_parser", "uuid", "web"]}, {"name": "just_audio_platform_interface", "version": "4.5.0", "dependencies": ["flutter", "plugin_platform_interface"]}, {"name": "logging", "version": "1.3.0", "dependencies": []}, {"name": "markdown", "version": "7.3.0", "dependencies": ["args", "meta"]}, {"name": "win32_registry", "version": "2.1.0", "dependencies": ["ffi", "meta", "win32"]}, {"name": "win32", "version": "5.13.0", "dependencies": ["ffi"]}, {"name": "web", "version": "1.1.1", "dependencies": []}, {"name": "file", "version": "7.0.1", "dependencies": ["meta", "path"]}, {"name": "ffi", "version": "2.1.4", "dependencies": []}, {"name": "device_info_plus_platform_interface", "version": "7.0.2", "dependencies": ["flutter", "meta", "plugin_platform_interface"]}, {"name": "freezed_annotation", "version": "2.4.4", "dependencies": ["collection", "json_annotation", "meta"]}, {"name": "uuid", "version": "4.5.1", "dependencies": ["crypto", "fixnum", "meta", "sprintf"]}, {"name": "flutter_rust_bridge", "version": "2.7.0", "dependencies": ["args", "async", "build_cli_annotations", "meta", "path", "web"]}, {"name": "cross_file", "version": "0.3.4+2", "dependencies": ["meta", "web"]}, {"name": "flutter_plugin_android_lifecycle", "version": "2.0.28", "dependencies": ["flutter"]}, {"name": "path_provider_windows", "version": "2.3.0", "dependencies": ["ffi", "flutter", "path", "path_provider_platform_interface"]}, {"name": "path_provider_platform_interface", "version": "2.1.2", "dependencies": ["flutter", "platform", "plugin_platform_interface"]}, {"name": "path_provider_linux", "version": "2.2.1", "dependencies": ["ffi", "flutter", "path", "path_provider_platform_interface", "xdg_directories"]}, {"name": "path_provider_foundation", "version": "2.4.1", "dependencies": ["flutter", "path_provider_platform_interface"]}, {"name": "path_provider_android", "version": "2.2.17", "dependencies": ["flutter", "path_provider_platform_interface"]}, {"name": "url_launcher_windows", "version": "3.1.4", "dependencies": ["flutter", "url_launcher_platform_interface"]}, {"name": "url_launcher_web", "version": "2.4.1", "dependencies": ["flutter", "flutter_web_plugins", "url_launcher_platform_interface", "web"]}, {"name": "url_launcher_platform_interface", "version": "2.3.2", "dependencies": ["flutter", "plugin_platform_interface"]}, {"name": "url_launcher_macos", "version": "3.2.2", "dependencies": ["flutter", "url_launcher_platform_interface"]}, {"name": "url_launcher_linux", "version": "3.2.1", "dependencies": ["flutter", "url_launcher_platform_interface"]}, {"name": "url_launcher_ios", "version": "6.3.3", "dependencies": ["flutter", "url_launcher_platform_interface"]}, {"name": "url_launcher_android", "version": "6.3.16", "dependencies": ["flutter", "url_launcher_platform_interface"]}, {"name": "synchronized", "version": "3.3.1", "dependencies": []}, {"name": "rxdart", "version": "0.28.0", "dependencies": []}, {"name": "audio_session", "version": "0.2.2", "dependencies": ["flutter", "flutter_web_plugins", "meta", "rxdart"]}, {"name": "audio_service", "version": "0.18.18", "dependencies": ["audio_service_platform_interface", "audio_service_web", "audio_session", "clock", "flutter", "flutter_cache_manager", "flutter_web_plugins", "js", "rxdart"]}, {"name": "crypto", "version": "3.0.6", "dependencies": ["typed_data"]}, {"name": "just_audio_web", "version": "0.4.16", "dependencies": ["flutter", "flutter_web_plugins", "just_audio_platform_interface", "synchronized", "web"]}, {"name": "nested", "version": "1.0.0", "dependencies": ["flutter"]}, {"name": "share_plus_platform_interface", "version": "6.0.0", "dependencies": ["cross_file", "flutter", "meta", "mime", "path_provider", "plugin_platform_interface", "uuid"]}, {"name": "mime", "version": "2.0.0", "dependencies": []}, {"name": "pointer_interceptor", "version": "0.10.1+2", "dependencies": ["flutter", "flutter_web_plugins", "pointer_interceptor_ios", "pointer_interceptor_platform_interface", "pointer_interceptor_web"]}, {"name": "flutter_keyboard_visibility", "version": "6.0.0", "dependencies": ["flutter", "flutter_keyboard_visibility_linux", "flutter_keyboard_visibility_macos", "flutter_keyboard_visibility_platform_interface", "flutter_keyboard_visibility_web", "flutter_keyboard_visibility_windows", "meta"]}, {"name": "octo_image", "version": "2.1.0", "dependencies": ["flutter"]}, {"name": "flutter_cache_manager", "version": "3.4.1", "dependencies": ["clock", "collection", "file", "flutter", "http", "path", "path_provider", "rxdart", "sqflite", "uuid"]}, {"name": "cached_network_image_web", "version": "1.3.1", "dependencies": ["cached_network_image_platform_interface", "flutter", "flutter_cache_manager", "web"]}, {"name": "cached_network_image_platform_interface", "version": "4.1.1", "dependencies": ["flutter", "flutter_cache_manager"]}, {"name": "unicode", "version": "1.1.8", "dependencies": ["archive", "simple_sparse_list"]}, {"name": "json_annotation", "version": "4.9.0", "dependencies": ["meta"]}, {"name": "http_parser", "version": "4.1.2", "dependencies": ["collection", "source_span", "string_scanner", "typed_data"]}, {"name": "sky_engine", "version": "0.0.0", "dependencies": []}, {"name": "petitparser", "version": "6.1.0", "dependencies": ["collection", "meta"]}, {"name": "typed_data", "version": "1.4.0", "dependencies": ["collection"]}, {"name": "archive", "version": "4.0.7", "dependencies": ["crypto", "path", "posix"]}, {"name": "csslib", "version": "1.0.2", "dependencies": ["source_span"]}, {"name": "file_selector_windows", "version": "0.9.3+4", "dependencies": ["cross_file", "file_selector_platform_interface", "flutter"]}, {"name": "file_selector_platform_interface", "version": "2.6.2", "dependencies": ["cross_file", "flutter", "http", "plugin_platform_interface"]}, {"name": "file_selector_macos", "version": "0.9.4+3", "dependencies": ["cross_file", "file_selector_platform_interface", "flutter"]}, {"name": "file_selector_linux", "version": "0.9.3+2", "dependencies": ["cross_file", "file_selector_platform_interface", "flutter"]}, {"name": "screen_retriever_windows", "version": "0.2.0", "dependencies": ["flutter", "screen_retriever_platform_interface"]}, {"name": "screen_retriever_platform_interface", "version": "0.2.0", "dependencies": ["flutter", "json_annotation", "plugin_platform_interface"]}, {"name": "screen_retriever_macos", "version": "0.2.0", "dependencies": ["flutter", "screen_retriever_platform_interface"]}, {"name": "screen_retriever_linux", "version": "0.2.0", "dependencies": ["flutter", "screen_retriever_platform_interface"]}, {"name": "uri_parser", "version": "3.0.0", "dependencies": ["path", "safe_local_storage"]}, {"name": "safe_local_storage", "version": "2.0.1", "dependencies": ["path", "synchronized"]}, {"name": "fixnum", "version": "1.1.1", "dependencies": []}, {"name": "sprintf", "version": "7.0.0", "dependencies": []}, {"name": "build_cli_annotations", "version": "2.1.0", "dependencies": ["args", "meta"]}, {"name": "platform", "version": "3.1.6", "dependencies": []}, {"name": "xdg_directories", "version": "1.1.0", "dependencies": ["meta", "path"]}, {"name": "js", "version": "0.7.2", "dependencies": []}, {"name": "audio_service_web", "version": "0.1.4", "dependencies": ["audio_service_platform_interface", "flutter", "flutter_web_plugins", "rxdart", "web"]}, {"name": "audio_service_platform_interface", "version": "0.1.3", "dependencies": ["flutter", "meta", "plugin_platform_interface"]}, {"name": "pointer_interceptor_web", "version": "0.10.2+1", "dependencies": ["flutter", "flutter_web_plugins", "plugin_platform_interface", "pointer_interceptor_platform_interface", "web"]}, {"name": "pointer_interceptor_platform_interface", "version": "0.10.0+1", "dependencies": ["flutter", "plugin_platform_interface"]}, {"name": "pointer_interceptor_ios", "version": "0.10.1", "dependencies": ["flutter", "plugin_platform_interface", "pointer_interceptor_platform_interface"]}, {"name": "flutter_keyboard_visibility_windows", "version": "1.0.0", "dependencies": ["flutter", "flutter_keyboard_visibility_platform_interface"]}, {"name": "flutter_keyboard_visibility_web", "version": "2.0.0", "dependencies": ["flutter", "flutter_keyboard_visibility_platform_interface", "flutter_web_plugins"]}, {"name": "flutter_keyboard_visibility_macos", "version": "1.0.0", "dependencies": ["flutter", "flutter_keyboard_visibility_platform_interface"]}, {"name": "flutter_keyboard_visibility_linux", "version": "1.0.0", "dependencies": ["flutter", "flutter_keyboard_visibility_platform_interface"]}, {"name": "flutter_keyboard_visibility_platform_interface", "version": "2.0.0", "dependencies": ["flutter", "meta", "plugin_platform_interface"]}, {"name": "sqflite", "version": "2.4.2", "dependencies": ["flutter", "path", "sqflite_android", "sqflite_common", "sqflite_darwin", "sqflite_platform_interface"]}, {"name": "simple_sparse_list", "version": "0.1.4", "dependencies": []}, {"name": "posix", "version": "6.0.2", "dependencies": ["ffi", "meta", "path"]}, {"name": "sqflite_common", "version": "2.5.5", "dependencies": ["meta", "path", "synchronized"]}, {"name": "sqflite_platform_interface", "version": "2.4.0", "dependencies": ["flutter", "meta", "platform", "plugin_platform_interface", "sqflite_common"]}, {"name": "sqflite_darwin", "version": "2.4.2", "dependencies": ["flutter", "meta", "path", "sqflite_common", "sqflite_platform_interface"]}, {"name": "sqflite_android", "version": "2.4.1", "dependencies": ["flutter", "path", "sqflite_common", "sqflite_platform_interface"]}], "configVersion": 1}